<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Test - Aanabi Pharmacy</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
    </style>
</head>
<body>
    <h1>Database Initialization Test</h1>
    
    <div id="status-container">
        <div class="status info">
            <strong>Status:</strong> <span id="db-status">Checking database...</span>
        </div>
    </div>

    <div>
        <h3>Test Actions</h3>
        <button class="btn-primary" onclick="testDatabaseReady()">Check Database Ready</button>
        <button class="btn-success" onclick="testGetMedicines()">Get Medicines</button>
        <button class="btn-success" onclick="testGenerateId()">Test Generate ID</button>
        <button class="btn-danger" onclick="clearDatabase()">Clear Database</button>
    </div>

    <div id="results">
        <h3>Results</h3>
        <pre id="results-content"></pre>
    </div>

    <script src="js/database.js"></script>
    <script>
        let statusElement = document.getElementById('db-status');
        let resultsElement = document.getElementById('results-content');

        function updateStatus(message, type = 'info') {
            statusElement.textContent = message;
            const container = statusElement.parentElement;
            container.className = `status ${type}`;
        }

        function addResult(message) {
            resultsElement.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
        }

        // Monitor database initialization
        let checkCount = 0;
        const checkInterval = setInterval(() => {
            checkCount++;
            
            if (dbManager.isReady()) {
                updateStatus('Database is ready!', 'success');
                addResult('Database initialized successfully');
                clearInterval(checkInterval);
            } else if (checkCount > 100) { // 10 seconds
                updateStatus('Database initialization timeout', 'error');
                addResult('Database failed to initialize within 10 seconds');
                clearInterval(checkInterval);
            } else {
                updateStatus(`Waiting for database... (${checkCount}/100)`, 'warning');
            }
        }, 100);

        async function testDatabaseReady() {
            try {
                const isReady = dbManager.isReady();
                addResult(`Database ready check: ${isReady}`);
                
                if (isReady) {
                    const medicines = await dbManager.getAll('medicines');
                    addResult(`Found ${medicines.length} medicines in database`);
                } else {
                    addResult('Database not ready - cannot perform operations');
                }
            } catch (error) {
                addResult(`Error checking database: ${error.message}`);
            }
        }

        async function testGetMedicines() {
            try {
                if (!dbManager.isReady()) {
                    addResult('Database not ready - cannot get medicines');
                    return;
                }
                
                const medicines = await dbManager.getAll('medicines');
                addResult(`Successfully retrieved ${medicines.length} medicines`);
                
                if (medicines.length > 0) {
                    addResult(`First medicine: ${medicines[0].medicine_name}`);
                }
            } catch (error) {
                addResult(`Error getting medicines: ${error.message}`);
            }
        }

        async function testGenerateId() {
            try {
                if (!dbManager.isReady()) {
                    addResult('Database not ready - cannot generate ID');
                    return;
                }
                
                const newId = await dbManager.generateId('medicines', 'MED');
                addResult(`Generated new medicine ID: ${newId}`);
            } catch (error) {
                addResult(`Error generating ID: ${error.message}`);
            }
        }

        async function clearDatabase() {
            if (!confirm('Are you sure you want to clear the database? This will delete all data.')) {
                return;
            }
            
            try {
                // Close current database connection
                if (dbManager.db) {
                    dbManager.db.close();
                }
                
                // Delete the database
                const deleteRequest = indexedDB.deleteDatabase('AanabiPharmacyDB');
                deleteRequest.onsuccess = () => {
                    addResult('Database cleared successfully');
                    updateStatus('Database cleared - refresh page to reinitialize', 'warning');
                };
                deleteRequest.onerror = () => {
                    addResult('Error clearing database');
                };
            } catch (error) {
                addResult(`Error clearing database: ${error.message}`);
            }
        }
    </script>
</body>
</html>
