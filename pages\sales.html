<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sales & Billing - Aanabi Pharmacy</title>
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">Aanabi Pharmacy Management System</div>
            <div class="header-info">
                <span id="current-date"></span> | <span id="current-time"></span>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav">
        <div class="nav-content">
            <ul class="nav-menu">
                <li class="nav-item"><a href="../index.html" class="nav-link">🏠 Dashboard</a></li>
                <li class="nav-item"><a href="medicines.html" class="nav-link">💊 Medicines</a></li>
                <li class="nav-item"><a href="inventory.html" class="nav-link">📦 Inventory</a></li>
                <li class="nav-item"><a href="sales.html" class="nav-link active">💳 Sales</a></li>
                <li class="nav-item"><a href="purchase.html" class="nav-link">🛒 Purchase</a></li>
                <li class="nav-item"><a href="customers.html" class="nav-link">👥 Customers</a></li>
                <li class="nav-item"><a href="suppliers.html" class="nav-link">🏭 Suppliers</a></li>
                <li class="nav-item"><a href="reports.html" class="nav-link">📊 Reports</a></li>
                <li class="nav-item"><a href="settings.html" class="nav-link">⚙️ Settings</a></li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Page Header -->
        <div class="card">
            <div class="card-header">
                <h1 class="card-title">💳 Sales & Billing</h1>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-8">
                        <button class="btn btn-primary" onclick="showNewSaleModal()">➕ New Sale</button>
                        <button class="btn btn-success" onclick="showQuickSaleModal()">⚡ Quick Sale</button>
                        <button class="btn btn-warning" onclick="exportSales()">📤 Export Sales</button>
                    </div>
                    <div class="col-4">
                        <div class="search-container">
                            <input type="text" id="sales-search" class="search-input" placeholder="Search sales...">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sales Statistics -->
        <div class="dashboard-stats">
            <div class="stat-card">
                <span class="stat-icon">💰</span>
                <div class="stat-value" id="today-sales-amount">Rs. 0</div>
                <div class="stat-label">Today's Sales</div>
            </div>
            <div class="stat-card">
                <span class="stat-icon">📊</span>
                <div class="stat-value" id="total-transactions">0</div>
                <div class="stat-label">Total Transactions</div>
            </div>
            <div class="stat-card">
                <span class="stat-icon">💵</span>
                <div class="stat-value" id="average-sale">Rs. 0</div>
                <div class="stat-label">Average Sale</div>
            </div>
            <div class="stat-card">
                <span class="stat-icon">🎯</span>
                <div class="stat-value" id="total-profit">Rs. 0</div>
                <div class="stat-label">Today's Profit</div>
            </div>
        </div>

        <!-- Sales List -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Recent Sales Transactions</h3>
            </div>
            <div class="card-body">
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Sale ID</th>
                                <th>Date</th>
                                <th>Time</th>
                                <th>Customer</th>
                                <th>Medicine</th>
                                <th>Quantity</th>
                                <th>Unit Price</th>
                                <th>Discount</th>
                                <th>Total Amount</th>
                                <th>Payment Method</th>
                                <th>Profit</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="sales-tbody">
                            <!-- Sales data will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- New Sale Modal -->
    <div id="sale-modal" class="modal-overlay" style="display: none;">
        <div class="modal" style="max-width: 800px;">
            <div class="modal-header">
                <h3 class="modal-title">New Sale Transaction</h3>
                <button class="modal-close" onclick="closeSaleModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="sale-form">
                    <!-- Customer Information -->
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Customer Information</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group">
                                        <label class="form-label">Customer Phone (Optional)</label>
                                        <input type="text" id="customer-phone" name="customer_phone" class="form-input" 
                                               placeholder="Enter phone number" onchange="searchCustomer()">
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group">
                                        <label class="form-label">Customer Name</label>
                                        <input type="text" id="customer-name" name="customer_name" class="form-input" 
                                               placeholder="Walk-in Customer">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Medicine Selection -->
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Medicine Selection</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-4">
                                    <div class="form-group">
                                        <label class="form-label">Medicine *</label>
                                        <select id="medicine-select" name="medicine_id" class="form-select" required onchange="loadMedicineDetails()">
                                            <option value="">Select Medicine</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-2">
                                    <div class="form-group">
                                        <label class="form-label">Available Stock</label>
                                        <input type="text" id="available-stock" class="form-input" readonly>
                                    </div>
                                </div>
                                <div class="col-2">
                                    <div class="form-group">
                                        <label class="form-label">Unit Price (Rs.)</label>
                                        <input type="number" id="unit-price" name="unit_price" class="form-input" readonly>
                                    </div>
                                </div>
                                <div class="col-2">
                                    <div class="form-group">
                                        <label class="form-label">Quantity *</label>
                                        <input type="number" id="quantity" name="quantity" class="form-input" 
                                               min="1" required onchange="calculateTotal()">
                                    </div>
                                </div>
                                <div class="col-2">
                                    <div class="form-group">
                                        <label class="form-label">Total</label>
                                        <input type="text" id="total-amount" class="form-input" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Billing Information -->
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Billing Information</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-3">
                                    <div class="form-group">
                                        <label class="form-label">Discount (%)</label>
                                        <input type="number" id="discount-percent" name="discount_percent" 
                                               class="form-input" min="0" max="100" value="0" onchange="calculateTotal()">
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="form-group">
                                        <label class="form-label">Discount Amount</label>
                                        <input type="text" id="discount-amount" class="form-input" readonly>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="form-group">
                                        <label class="form-label">Tax Amount (13%)</label>
                                        <input type="text" id="tax-amount" class="form-input" readonly>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="form-group">
                                        <label class="form-label">Payment Method *</label>
                                        <select id="payment-method" name="payment_method" class="form-select" required>
                                            <option value="">Select Method</option>
                                            <option value="Cash">Cash</option>
                                            <option value="Card">Card</option>
                                            <option value="Digital Payment">Digital Payment</option>
                                            <option value="Credit">Credit</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group">
                                        <label class="form-label" style="font-size: 1.2rem; font-weight: bold;">Final Total Amount (Rs.)</label>
                                        <input type="text" id="final-total" class="form-input" readonly 
                                               style="font-size: 1.5rem; font-weight: bold; color: #2c3e50;">
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group">
                                        <label class="form-label">Profit Amount</label>
                                        <input type="text" id="profit-amount" class="form-input" readonly>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <button type="submit" class="btn btn-primary btn-lg">💳 Complete Sale</button>
                                <button type="button" class="btn btn-danger" onclick="closeSaleModal()">Cancel</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Quick Sale Modal -->
    <div id="quick-sale-modal" class="modal-overlay" style="display: none;">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">⚡ Quick Sale</h3>
                <button class="modal-close" onclick="closeQuickSaleModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="quick-sale-form">
                    <div class="form-group">
                        <label class="form-label">Medicine *</label>
                        <select id="quick-medicine-select" name="medicine_id" class="form-select" required onchange="loadQuickMedicineDetails()">
                            <option value="">Select Medicine</option>
                        </select>
                    </div>
                    
                    <div class="row">
                        <div class="col-4">
                            <div class="form-group">
                                <label class="form-label">Available</label>
                                <input type="text" id="quick-available-stock" class="form-input" readonly>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="form-group">
                                <label class="form-label">Quantity *</label>
                                <input type="number" id="quick-quantity" name="quantity" class="form-input" 
                                       min="1" required onchange="calculateQuickTotal()">
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="form-group">
                                <label class="form-label">Unit Price</label>
                                <input type="text" id="quick-unit-price" class="form-input" readonly>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Payment Method *</label>
                                <select id="quick-payment-method" name="payment_method" class="form-select" required>
                                    <option value="Cash">Cash</option>
                                    <option value="Card">Card</option>
                                    <option value="Digital Payment">Digital Payment</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label" style="font-weight: bold;">Total Amount</label>
                                <input type="text" id="quick-total" class="form-input" readonly 
                                       style="font-weight: bold; color: #2c3e50;">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary btn-lg">⚡ Quick Sale</button>
                        <button type="button" class="btn btn-danger" onclick="closeQuickSaleModal()">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bill Print Modal -->
    <div id="bill-modal" class="modal-overlay" style="display: none;">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">🧾 Sale Receipt</h3>
                <button class="modal-close" onclick="closeBillModal()">&times;</button>
            </div>
            <div class="modal-body" id="bill-content">
                <!-- Bill content will be generated here -->
            </div>
            <div style="padding: 1rem; border-top: 1px solid #e0e0e0; text-align: center;">
                <button class="btn btn-primary" onclick="printBillFromModal()">🖨️ Print Bill</button>
                <button class="btn btn-danger" onclick="closeBillModal()">Close</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/database.js"></script>
    <script src="../js/sales.js"></script>
</body>
</html>