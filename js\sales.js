// Sales management functionality

class SalesManager {
    constructor() {
        this.sales = [];
        this.medicines = [];
        this.customers = [];
        this.currentSale = null;
        this.vatRate = 13; // 13% VAT
        this.init();
    }

    async init() {
        await this.loadData();
        this.setupEventListeners();
        this.updateDateTime();
        setInterval(() => this.updateDateTime(), 1000);
    }

    updateDateTime() {
        const now = new Date();
        const dateOptions = { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        };
        const timeOptions = { 
            hour: '2-digit', 
            minute: '2-digit', 
            second: '2-digit' 
        };

        const dateElement = document.getElementById('current-date');
        const timeElement = document.getElementById('current-time');
        
        if (dateElement && timeElement) {
            dateElement.textContent = now.toLocaleDateString('en-US', dateOptions);
            timeElement.textContent = now.toLocaleTimeString('en-US', timeOptions);
        }
    }

    async loadData() {
        try {
            // Wait for database to be ready
            if (!dbManager.isReady()) {
                setTimeout(() => this.loadData(), 1000);
                return;
            }

            this.sales = await dbManager.getAll('sales');
            this.medicines = await dbManager.getAll('medicines');
            this.customers = await dbManager.getAll('customers');

            this.populateMedicineDropdowns();
            this.renderSales();
            this.updateStatistics();
        } catch (error) {
            console.error('Error loading sales data:', error);
        }
    }

    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('sales-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => this.filterSales(e.target.value));
        }

        // Form submissions
        const saleForm = document.getElementById('sale-form');
        if (saleForm) {
            saleForm.addEventListener('submit', (e) => this.handleSaleSubmit(e));
        }

        const quickSaleForm = document.getElementById('quick-sale-form');
        if (quickSaleForm) {
            quickSaleForm.addEventListener('submit', (e) => this.handleQuickSaleSubmit(e));
        }
    }

    populateMedicineDropdowns() {
        const activeMedicines = this.medicines.filter(m => m.status === 'Active' && m.current_stock > 0);
        
        // Regular sale dropdown
        const medicineSelect = document.getElementById('medicine-select');
        if (medicineSelect) {
            medicineSelect.innerHTML = '<option value="">Select Medicine</option>';
            activeMedicines.forEach(medicine => {
                const option = document.createElement('option');
                option.value = medicine.medicine_id;
                option.textContent = `${medicine.medicine_name} (Stock: ${medicine.current_stock})`;
                medicineSelect.appendChild(option);
            });
        }

        // Quick sale dropdown
        const quickMedicineSelect = document.getElementById('quick-medicine-select');
        if (quickMedicineSelect) {
            quickMedicineSelect.innerHTML = '<option value="">Select Medicine</option>';
            activeMedicines.forEach(medicine => {
                const option = document.createElement('option');
                option.value = medicine.medicine_id;
                option.textContent = `${medicine.medicine_name} (Stock: ${medicine.current_stock})`;
                quickMedicineSelect.appendChild(option);
            });
        }
    }

    renderSales() {
        const tbody = document.getElementById('sales-tbody');
        if (!tbody) return;

        tbody.innerHTML = '';

        if (this.sales.length === 0) {
            tbody.innerHTML = '<tr><td colspan="12" class="text-center text-muted">No sales found</td></tr>';
            return;
        }

        // Sort sales by date (newest first)
        const sortedSales = [...this.sales].sort((a, b) => new Date(b.date + ' ' + b.time) - new Date(a.date + ' ' + a.time));

        sortedSales.forEach(sale => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${sale.sale_id}</td>
                <td>${new Date(sale.date).toLocaleDateString()}</td>
                <td>${sale.time}</td>
                <td>${sale.customer_name || 'Walk-in Customer'}</td>
                <td>${sale.medicine_name}</td>
                <td>${sale.quantity}</td>
                <td>Rs. ${parseFloat(sale.unit_price).toFixed(2)}</td>
                <td>${sale.discount_percent}%</td>
                <td>Rs. ${parseFloat(sale.final_total).toFixed(2)}</td>
                <td><span class="status status-paid">${sale.payment_method}</span></td>
                <td>Rs. ${parseFloat(sale.profit_amount || 0).toFixed(2)}</td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="printSaleBill('${sale.sale_id}')">🖨️ Print</button>
                    <button class="btn btn-sm btn-danger" onclick="deleteSale('${sale.sale_id}')">Delete</button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    filterSales(searchTerm) {
        const term = searchTerm.toLowerCase();
        const filteredSales = this.sales.filter(sale => 
            sale.sale_id.toLowerCase().includes(term) ||
            (sale.customer_name && sale.customer_name.toLowerCase().includes(term)) ||
            sale.medicine_name.toLowerCase().includes(term) ||
            sale.payment_method.toLowerCase().includes(term)
        );
        
        // Re-render with filtered results
        const tbody = document.getElementById('sales-tbody');
        if (!tbody) return;

        tbody.innerHTML = '';

        if (filteredSales.length === 0) {
            tbody.innerHTML = '<tr><td colspan="12" class="text-center text-muted">No matching sales found</td></tr>';
            return;
        }

        filteredSales.forEach(sale => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${sale.sale_id}</td>
                <td>${new Date(sale.date).toLocaleDateString()}</td>
                <td>${sale.time}</td>
                <td>${sale.customer_name || 'Walk-in Customer'}</td>
                <td>${sale.medicine_name}</td>
                <td>${sale.quantity}</td>
                <td>Rs. ${parseFloat(sale.unit_price).toFixed(2)}</td>
                <td>${sale.discount_percent}%</td>
                <td>Rs. ${parseFloat(sale.final_total).toFixed(2)}</td>
                <td><span class="status status-paid">${sale.payment_method}</span></td>
                <td>Rs. ${parseFloat(sale.profit_amount || 0).toFixed(2)}</td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="printSaleBill('${sale.sale_id}')">🖨️ Print</button>
                    <button class="btn btn-sm btn-danger" onclick="deleteSale('${sale.sale_id}')">Delete</button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    updateStatistics() {
        const today = new Date().toISOString().split('T')[0];
        const todaySales = this.sales.filter(sale => sale.date === today);
        
        const todayAmount = todaySales.reduce((sum, sale) => sum + parseFloat(sale.final_total || 0), 0);
        const totalTransactions = todaySales.length;
        const averageSale = totalTransactions > 0 ? todayAmount / totalTransactions : 0;
        const totalProfit = todaySales.reduce((sum, sale) => sum + parseFloat(sale.profit_amount || 0), 0);

        document.getElementById('today-sales-amount').textContent = `Rs. ${todayAmount.toLocaleString()}`;
        document.getElementById('total-transactions').textContent = totalTransactions;
        document.getElementById('average-sale').textContent = `Rs. ${averageSale.toFixed(0)}`;
        document.getElementById('total-profit').textContent = `Rs. ${totalProfit.toLocaleString()}`;
    }

    async handleSaleSubmit(event) {
        event.preventDefault();

        // Check if database is ready
        if (!dbManager.isReady()) {
            this.showAlert('Database is not ready. Please wait a moment and try again.', 'warning');
            return;
        }

        const formData = new FormData(event.target);
        const saleData = Object.fromEntries(formData.entries());

        try {
            // Validate stock availability
            const medicine = this.medicines.find(m => m.medicine_id === saleData.medicine_id);
            if (!medicine) {
                this.showAlert('Medicine not found', 'danger');
                return;
            }

            const quantity = parseInt(saleData.quantity);
            if (quantity > medicine.current_stock) {
                this.showAlert(`Insufficient stock. Available: ${medicine.current_stock}`, 'danger');
                return;
            }

            // Calculate all amounts
            const unitPrice = parseFloat(medicine.selling_price);
            const totalAmount = quantity * unitPrice;
            const discountPercent = parseFloat(saleData.discount_percent) || 0;
            const discountAmount = (totalAmount * discountPercent) / 100;
            const afterDiscount = totalAmount - discountAmount;
            const taxAmount = (afterDiscount * this.vatRate) / 100;
            const finalTotal = afterDiscount + taxAmount;
            const profitAmount = (unitPrice - parseFloat(medicine.purchase_price)) * quantity;

            // Prepare sale record
            const sale = {
                sale_id: await dbManager.generateId('sales', 'SALE'),
                date: new Date().toISOString().split('T')[0],
                time: new Date().toLocaleTimeString('en-US', { hour12: false }),
                customer_name: saleData.customer_name || 'Walk-in Customer',
                customer_phone: saleData.customer_phone || '',
                medicine_id: saleData.medicine_id,
                medicine_name: medicine.medicine_name,
                quantity: quantity,
                unit_price: unitPrice,
                total_amount: totalAmount,
                discount_percent: discountPercent,
                discount_amount: discountAmount,
                tax_amount: taxAmount,
                final_total: finalTotal,
                payment_method: saleData.payment_method,
                profit_amount: profitAmount
            };

            // Save sale
            await dbManager.insert('sales', sale);

            // Update medicine stock
            medicine.current_stock -= quantity;
            await dbManager.update('medicines', medicine);

            // Update customer loyalty points if phone provided
            if (saleData.customer_phone) {
                await this.updateCustomerLoyalty(saleData.customer_phone, finalTotal);
            }

            this.showAlert('Sale completed successfully!', 'success');
            this.closeSaleModal();
            await this.loadData();

            // Show print option
            if (confirm('Sale completed! Do you want to print the bill?')) {
                this.printBill(sale);
            }

        } catch (error) {
            console.error('Error processing sale:', error);
            this.showAlert('Error processing sale. Please try again.', 'danger');
        }
    }

    async handleQuickSaleSubmit(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        const saleData = Object.fromEntries(formData.entries());
        
        try {
            const medicine = this.medicines.find(m => m.medicine_id === saleData.medicine_id);
            if (!medicine) {
                this.showAlert('Medicine not found', 'danger');
                return;
            }

            const quantity = parseInt(saleData.quantity);
            if (quantity > medicine.current_stock) {
                this.showAlert(`Insufficient stock. Available: ${medicine.current_stock}`, 'danger');
                return;
            }

            const unitPrice = parseFloat(medicine.selling_price);
            const totalAmount = quantity * unitPrice;
            const taxAmount = (totalAmount * this.vatRate) / 100;
            const finalTotal = totalAmount + taxAmount;
            const profitAmount = (unitPrice - parseFloat(medicine.purchase_price)) * quantity;

            const sale = {
                sale_id: await dbManager.generateId('sales', 'SALE'),
                date: new Date().toISOString().split('T')[0],
                time: new Date().toLocaleTimeString('en-US', { hour12: false }),
                customer_name: 'Walk-in Customer',
                customer_phone: '',
                medicine_id: saleData.medicine_id,
                medicine_name: medicine.medicine_name,
                quantity: quantity,
                unit_price: unitPrice,
                total_amount: totalAmount,
                discount_percent: 0,
                discount_amount: 0,
                tax_amount: taxAmount,
                final_total: finalTotal,
                payment_method: saleData.payment_method,
                profit_amount: profitAmount
            };

            await dbManager.insert('sales', sale);

            medicine.current_stock -= quantity;
            await dbManager.update('medicines', medicine);

            this.showAlert('Quick sale completed!', 'success');
            this.closeQuickSaleModal();
            await this.loadData();

        } catch (error) {
            console.error('Error processing quick sale:', error);
            this.showAlert('Error processing sale. Please try again.', 'danger');
        }
    }

    async updateCustomerLoyalty(phone, amount) {
        try {
            const customers = await dbManager.search('customers', 'phone', phone);
            if (customers.length > 0) {
                const customer = customers[0];
                customer.total_purchase_amount = (parseFloat(customer.total_purchase_amount) || 0) + amount;
                customer.loyalty_points = Math.floor(customer.total_purchase_amount / 20); // 1 point per Rs. 20
                customer.last_purchase_date = new Date().toISOString().split('T')[0];
                await dbManager.update('customers', customer);
            }
        } catch (error) {
            console.error('Error updating customer loyalty:', error);
        }
    }

    printBill(sale) {
        const billContent = `
            <div style="max-width: 400px; margin: 0 auto; font-family: monospace; font-size: 12px;">
                <div style="text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; margin-bottom: 10px;">
                    <h2 style="margin: 0;">🏥 AANABI PHARMACY</h2>
                    <p style="margin: 5px 0;">Main Street, Kathmandu, Nepal</p>
                    <p style="margin: 5px 0;">Phone: 01-4567890</p>
                    <p style="margin: 5px 0;">Email: <EMAIL></p>
                </div>
                
                <div style="margin-bottom: 15px;">
                    <div style="display: flex; justify-content: space-between;">
                        <span>Bill No: ${sale.sale_id}</span>
                        <span>Date: ${new Date(sale.date).toLocaleDateString()}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between;">
                        <span>Customer: ${sale.customer_name}</span>
                        <span>Time: ${sale.time}</span>
                    </div>
                </div>
                
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 15px;">
                    <thead>
                        <tr style="border-bottom: 1px solid #000;">
                            <th style="text-align: left; padding: 5px;">Item</th>
                            <th style="text-align: center; padding: 5px;">Qty</th>
                            <th style="text-align: right; padding: 5px;">Rate</th>
                            <th style="text-align: right; padding: 5px;">Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td style="padding: 5px;">${sale.medicine_name}</td>
                            <td style="text-align: center; padding: 5px;">${sale.quantity}</td>
                            <td style="text-align: right; padding: 5px;">Rs. ${parseFloat(sale.unit_price).toFixed(2)}</td>
                            <td style="text-align: right; padding: 5px;">Rs. ${parseFloat(sale.total_amount).toFixed(2)}</td>
                        </tr>
                    </tbody>
                </table>
                
                <div style="border-top: 1px solid #000; padding-top: 10px;">
                    <div style="display: flex; justify-content: space-between;">
                        <span>Subtotal:</span>
                        <span>Rs. ${parseFloat(sale.total_amount).toFixed(2)}</span>
                    </div>
                    ${sale.discount_amount > 0 ? `
                    <div style="display: flex; justify-content: space-between;">
                        <span>Discount (${sale.discount_percent}%):</span>
                        <span>- Rs. ${parseFloat(sale.discount_amount).toFixed(2)}</span>
                    </div>` : ''}
                    <div style="display: flex; justify-content: space-between;">
                        <span>VAT (13%):</span>
                        <span>Rs. ${parseFloat(sale.tax_amount).toFixed(2)}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; font-weight: bold; font-size: 14px; border-top: 1px solid #000; padding-top: 5px;">
                        <span>TOTAL:</span>
                        <span>Rs. ${parseFloat(sale.final_total).toFixed(2)}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-top: 5px;">
                        <span>Payment:</span>
                        <span>${sale.payment_method}</span>
                    </div>
                </div>
                
                <div style="text-align: center; margin-top: 20px; border-top: 1px solid #000; padding-top: 10px;">
                    <p style="margin: 5px 0;">Thank you for your purchase!</p>
                    <p style="margin: 5px 0;">Take care of your health</p>
                    <p style="margin: 5px 0; font-size: 10px;">Generated on ${new Date().toLocaleString()}</p>
                </div>
            </div>
        `;

        document.getElementById('bill-content').innerHTML = billContent;
        document.getElementById('bill-modal').style.display = 'flex';
    }

    printBillContent() {
        const billContent = document.getElementById('bill-content').innerHTML;
        const printWindow = window.open('', '_blank', 'width=600,height=800');
        
        printWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>Sales Receipt - Aanabi Pharmacy</title>
                <style>
                    @media print {
                        body { margin: 0; }
                        .no-print { display: none; }
                    }
                    body {
                        font-family: monospace;
                        font-size: 12px;
                        margin: 20px;
                    }
                    .print-button {
                        position: fixed;
                        top: 10px;
                        right: 10px;
                        padding: 10px 20px;
                        background: #3498db;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        cursor: pointer;
                    }
                    .print-button:hover {
                        background: #2980b9;
                    }
                </style>
            </head>
            <body>
                <button class="print-button no-print" onclick="window.print(); window.close();">🖨️ Print Bill</button>
                ${billContent}
            </body>
            </html>
        `);
        
        printWindow.document.close();
        printWindow.focus();
    }

    showAlert(message, type) {
        const existingAlerts = document.querySelectorAll('.alert');
        existingAlerts.forEach(alert => alert.remove());

        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;
        alert.textContent = message;
        
        const mainContent = document.querySelector('.main-content');
        mainContent.insertBefore(alert, mainContent.firstChild);

        setTimeout(() => alert.remove(), 3000);
    }

    closeSaleModal() {
        document.getElementById('sale-modal').style.display = 'none';
        document.getElementById('sale-form').reset();
    }

    closeQuickSaleModal() {
        document.getElementById('quick-sale-modal').style.display = 'none';
        document.getElementById('quick-sale-form').reset();
    }

    closeBillModal() {
        document.getElementById('bill-modal').style.display = 'none';
    }
}

// Global functions
function showNewSaleModal() {
    document.getElementById('sale-modal').style.display = 'flex';
    document.getElementById('sale-form').reset();
}

function showQuickSaleModal() {
    document.getElementById('quick-sale-modal').style.display = 'flex';
    document.getElementById('quick-sale-form').reset();
}

function closeSaleModal() {
    salesManager.closeSaleModal();
}

function closeQuickSaleModal() {
    salesManager.closeQuickSaleModal();
}

function closeBillModal() {
    salesManager.closeBillModal();
}

async function searchCustomer() {
    const phone = document.getElementById('customer-phone').value;
    if (phone.length >= 10) {
        try {
            const customers = await dbManager.search('customers', 'phone', phone);
            if (customers.length > 0) {
                const customer = customers[0];
                document.getElementById('customer-name').value = customer.customer_name;
            }
        } catch (error) {
            console.error('Error searching customer:', error);
        }
    }
}

function loadMedicineDetails() {
    const medicineId = document.getElementById('medicine-select').value;
    const medicine = salesManager.medicines.find(m => m.medicine_id === medicineId);
    
    if (medicine) {
        document.getElementById('available-stock').value = medicine.current_stock;
        document.getElementById('unit-price').value = medicine.selling_price;
        document.getElementById('quantity').max = medicine.current_stock;
        calculateTotal();
    } else {
        document.getElementById('available-stock').value = '';
        document.getElementById('unit-price').value = '';
        document.getElementById('quantity').max = '';
    }
}

function loadQuickMedicineDetails() {
    const medicineId = document.getElementById('quick-medicine-select').value;
    const medicine = salesManager.medicines.find(m => m.medicine_id === medicineId);
    
    if (medicine) {
        document.getElementById('quick-available-stock').value = medicine.current_stock;
        document.getElementById('quick-unit-price').value = `Rs. ${medicine.selling_price}`;
        document.getElementById('quick-quantity').max = medicine.current_stock;
        calculateQuickTotal();
    } else {
        document.getElementById('quick-available-stock').value = '';
        document.getElementById('quick-unit-price').value = '';
        document.getElementById('quick-quantity').max = '';
    }
}

function calculateTotal() {
    const unitPrice = parseFloat(document.getElementById('unit-price').value) || 0;
    const quantity = parseInt(document.getElementById('quantity').value) || 0;
    const discountPercent = parseFloat(document.getElementById('discount-percent').value) || 0;
    
    const totalAmount = unitPrice * quantity;
    const discountAmount = (totalAmount * discountPercent) / 100;
    const afterDiscount = totalAmount - discountAmount;
    const taxAmount = (afterDiscount * salesManager.vatRate) / 100;
    const finalTotal = afterDiscount + taxAmount;
    
    // Get medicine to calculate profit
    const medicineId = document.getElementById('medicine-select').value;
    const medicine = salesManager.medicines.find(m => m.medicine_id === medicineId);
    const profitAmount = medicine ? (unitPrice - parseFloat(medicine.purchase_price)) * quantity : 0;
    
    document.getElementById('total-amount').value = `Rs. ${totalAmount.toFixed(2)}`;
    document.getElementById('discount-amount').value = `Rs. ${discountAmount.toFixed(2)}`;
    document.getElementById('tax-amount').value = `Rs. ${taxAmount.toFixed(2)}`;
    document.getElementById('final-total').value = `Rs. ${finalTotal.toFixed(2)}`;
    document.getElementById('profit-amount').value = `Rs. ${profitAmount.toFixed(2)}`;
}

function calculateQuickTotal() {
    const medicineId = document.getElementById('quick-medicine-select').value;
    const medicine = salesManager.medicines.find(m => m.medicine_id === medicineId);
    const quantity = parseInt(document.getElementById('quick-quantity').value) || 0;
    
    if (medicine && quantity > 0) {
        const unitPrice = parseFloat(medicine.selling_price);
        const totalAmount = unitPrice * quantity;
        const taxAmount = (totalAmount * salesManager.vatRate) / 100;
        const finalTotal = totalAmount + taxAmount;
        
        document.getElementById('quick-total').value = `Rs. ${finalTotal.toFixed(2)}`;
    } else {
        document.getElementById('quick-total').value = '';
    }
}

async function printSaleBill(saleId) {
    try {
        const sale = await dbManager.get('sales', saleId);
        if (sale) {
            salesManager.printBill(sale);
        }
    } catch (error) {
        console.error('Error loading sale for print:', error);
        alert('Error loading sale data');
    }
}

function printBill() {
    salesManager.printBillContent();
}

function printBillFromModal() {
    salesManager.printBillContent();
}

async function deleteSale(saleId) {
    if (!confirm('Are you sure you want to delete this sale? This action cannot be undone.')) {
        return;
    }

    try {
        // Get sale details to restore stock
        const sale = await dbManager.get('sales', saleId);
        if (sale) {
            // Restore medicine stock
            const medicine = await dbManager.get('medicines', sale.medicine_id);
            if (medicine) {
                medicine.current_stock += sale.quantity;
                await dbManager.update('medicines', medicine);
            }
        }

        await dbManager.delete('sales', saleId);
        salesManager.showAlert('Sale deleted successfully!', 'success');
        await salesManager.loadData();
    } catch (error) {
        console.error('Error deleting sale:', error);
        salesManager.showAlert('Error deleting sale. Please try again.', 'danger');
    }
}

function exportSales() {
    try {
        const headers = [
            'Sale ID', 'Date', 'Time', 'Customer Name', 'Customer Phone',
            'Medicine Name', 'Quantity', 'Unit Price', 'Total Amount',
            'Discount %', 'Discount Amount', 'Tax Amount', 'Final Total',
            'Payment Method', 'Profit Amount'
        ];
        
        let csvContent = headers.join(',') + '\n';
        
        salesManager.sales.forEach(sale => {
            const row = [
                sale.sale_id,
                sale.date,
                sale.time,
                `"${sale.customer_name || ''}"`,
                sale.customer_phone || '',
                `"${sale.medicine_name}"`,
                sale.quantity,
                sale.unit_price,
                sale.total_amount,
                sale.discount_percent,
                sale.discount_amount,
                sale.tax_amount,
                sale.final_total,
                sale.payment_method,
                sale.profit_amount
            ];
            csvContent += row.join(',') + '\n';
        });
        
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `sales_export_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        salesManager.showAlert('Sales data exported successfully!', 'success');
    } catch (error) {
        console.error('Error exporting sales:', error);
        salesManager.showAlert('Error exporting sales data. Please try again.', 'danger');
    }
}

// Initialize sales manager when page loads
let salesManager;
document.addEventListener('DOMContentLoaded', () => {
    salesManager = new SalesManager();
});